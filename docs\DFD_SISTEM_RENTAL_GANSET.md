# 📊 DATA FLOW DIAGRAM (DFD) - SISTEM RENTAL GANSET

## 📋 OVERVIEW

Data Flow Diagram (DFD) ini dibuat berdasarkan aturan yang ditetapkan oleh:

- **<PERSON> (1979)** - Structured Analysis
- **<PERSON><PERSON> (1979)** - Structured Systems Analysis
- **<PERSON> (1979)** - Modern Structured Analysis

## 🎯 TUJUAN DFD

DFD ini menggambarkan aliran data dalam Sistem Rental Ganset yang mencakup:

- Proses autentikasi dan manajemen user
- Manajemen produk genset
- Proses pemesanan dan rental
- Sistem pembayaran terintegrasi dengan Midtrans
- Notifikasi WhatsApp otomatis
- Laporan dan analitik

## 📐 ATURAN DFD YANG DITERAPKAN

### ✅ ATURAN YANG DIPERBOLEHKAN

- **Koneksi Valid:**

  - External Entity → Process ✓
  - Process → External Entity ✓
  - Process → Data Store ✓
  - Data Store → Process ✓
  - Process → Process ✓

- **<PERSON><PERSON><PERSON> yang Benar:**
  - Proses: Kata kerja + <PERSON><PERSON><PERSON><PERSON> (contoh: "Kelola Pemesanan")
  - Data Flow: Kata benda (contoh: "Data Pemesanan")
  - Data Store: Kata benda jamak (contoh: "Data User")
  - External Entity: Nama role/sistem (contoh: "Customer")

### ❌ ATURAN YANG DIHINDARI

- **Larangan Koneksi:**

  - Data Store ke Data Store ❌
  - External Entity ke External Entity ❌
  - External Entity ke Data Store ❌
  - Data Store ke External Entity ❌

- **Larangan Struktural:**
  - Black Hole (proses tanpa output) ❌
  - Miracle (proses tanpa input) ❌
  - Gray Hole (input tidak cukup untuk output) ❌

## 🏗️ STRUKTUR DFD

### 📊 CONTEXT DIAGRAM (Level 0)

**Entitas Eksternal:**

- **Customer/User** - Pengguna sistem rental
- **Admin** - Administrator sistem
- **Midtrans Payment Gateway** - Sistem pembayaran
- **WhatsApp Service** - Layanan notifikasi

**Proses Utama:**

- **Sistem Rental Ganset** - Sistem utama yang mengelola seluruh proses rental

### 📊 DFD LEVEL 1

**Proses Utama (6 Proses):**

1. **Kelola Autentikasi** - Manajemen login, registrasi, session
2. **Kelola Produk** - CRUD produk genset, stok, maintenance
3. **Kelola Pemesanan** - Proses rental, validasi, konfirmasi
4. **Kelola Pembayaran** - Payment processing, invoice, overtime
5. **Kelola Notifikasi** - WhatsApp notifications, internal alerts
6. **Generate Laporan** - Reporting dan analytics

**Data Store (7 Storage):**

- **D1 Data User** - Informasi pengguna dan admin
- **D2 Data Produk** - Katalog genset dan spesifikasi
- **D3 Data Rental** - Record pemesanan dan status
- **D4 Data Payment** - Transaksi dan pembayaran
- **D5 Data Notification** - Log notifikasi sistem
- **D6 Data Review** - Review dan rating produk
- **D7 Data Session** - Session management

### 📊 DFD LEVEL 2

#### 🔄 Proses 3: Kelola Pemesanan

**Sub-Proses:**

- **3.1 Validasi Pemesanan** - Cek ketersediaan, validasi data
- **3.2 Hitung Biaya** - Kalkulasi harga, deposit, overtime
- **3.3 Buat Rental** - Create rental record
- **3.4 Konfirmasi Rental** - Admin confirmation process
- **3.5 Update Status** - Status tracking dan update
- **3.6 Kelola Review** - Customer review management

#### 💳 Proses 4: Kelola Pembayaran

**Sub-Proses:**

- **4.1 Buat Payment Token** - Generate Midtrans token
- **4.2 Proses Deposit** - Handle deposit payment (50%)
- **4.3 Proses Sisa Bayar** - Handle remaining payment (50%)
- **4.4 Verifikasi Pembayaran** - Webhook verification
- **4.5 Generate Invoice** - PDF invoice generation
- **4.6 Hitung Overtime** - Overtime cost calculation

#### 📱 Proses 5: Kelola Notifikasi

**Sub-Proses:**

- **5.1 Generate Notifikasi** - Create notification messages
- **5.2 Kirim WhatsApp Admin** - Admin WhatsApp alerts
- **5.3 Kirim WhatsApp Customer** - Customer WhatsApp updates
- **5.4 Kirim Notifikasi Internal** - In-app notifications
- **5.5 Update Status Notifikasi** - Notification status tracking

## 🔍 VALIDASI DFD

### ✅ Pemeriksaan Sintaks

- [x] Semua proses memiliki input dan output
- [x] Tidak ada koneksi terlarang
- [x] Semua komponen diberi label yang jelas
- [x] Penomoran konsisten dan berurutan

### ✅ Pemeriksaan Semantik

- [x] Nama proses menggunakan kata kerja
- [x] Nama data flow menggunakan kata benda
- [x] Tidak ada black hole atau miracle
- [x] Balancing antar level terjaga

### ✅ Pemeriksaan Pragmatik

- [x] DFD mudah dipahami stakeholder
- [x] Level abstraksi tepat untuk sistem rental
- [x] Tidak terlalu kompleks (maksimal 7±2 proses per level)
- [x] Mendukung tujuan analisis sistem

## 🚀 IMPLEMENTASI SISTEM

### 🛠️ Teknologi yang Digunakan

- **Backend:** Next.js 14 dengan App Router
- **Database:** PostgreSQL dengan Prisma ORM
- **Authentication:** Better Auth
- **Payment:** Midtrans Payment Gateway
- **Notifications:** WhatsApp API (Fonnte)
- **UI:** React dengan shadcn/ui components

### 📱 Fitur Utama

- **Multi-role Authentication** (User/Admin)
- **Real-time WhatsApp Notifications**
- **Integrated Payment System** (Deposit + Remaining)
- **PDF Invoice Generation**
- **Overtime Cost Calculation**
- **Mobile-first Responsive Design**
- **Admin Dashboard & Analytics**

## 📈 MANFAAT DFD

### 👥 Untuk Stakeholder

- **Pemahaman Sistem** - Visualisasi alur data yang jelas
- **Komunikasi** - Bahasa universal untuk tim development
- **Validasi Requirement** - Memastikan semua kebutuhan tercakup

### 💻 Untuk Developer

- **Panduan Development** - Roadmap implementasi yang terstruktur
- **Debugging** - Identifikasi bottleneck dan error points
- **Maintenance** - Dokumentasi untuk future updates

### 🏢 Untuk Business

- **Process Optimization** - Identifikasi area improvement
- **Risk Management** - Antisipasi failure points
- **Scalability Planning** - Persiapan untuk growth

## 🔧 DETAIL TEKNIS IMPLEMENTASI

### 📊 Mapping DFD ke Kode

#### Proses 1: Kelola Autentikasi

```typescript
// lib/auth/config.ts - Better Auth Configuration
export const auth = betterAuth({
  database: prismaAdapter(prisma, { provider: "postgresql" }),
  emailAndPassword: { enabled: true },
  user: {
    additionalFields: {
      role: { type: "string", defaultValue: "USER" },
      phone: { type: "string", required: false },
    },
  },
});
```

#### Proses 3: Kelola Pemesanan

```typescript
// lib/actions/rental.ts - Create Rental Process
export async function createRental(formData: FormData): Promise<RentalResult> {
  // 3.1 Validasi Pemesanan
  const session = await getSession();
  const product = await prisma.product.findUnique({...});

  // 3.2 Hitung Biaya
  const amount = calculateRentalCost(product, duration, quantity);

  // 3.3 Buat Rental
  const rental = await prisma.rental.create({...});

  // Trigger Proses 5: Notifikasi
  await WhatsAppService.sendAdminOrderNotification(...);
}
```

#### Proses 4: Kelola Pembayaran

```typescript
// lib/services/midtrans.ts - Payment Processing
export async function createPayment(params: PaymentParams) {
  // 4.1 Buat Payment Token
  const parameter = {
    transaction_details: { order_id, gross_amount },
    customer_details: { first_name, email },
  };

  // 4.2/4.3 Proses Deposit/Sisa Bayar
  const transaction = await snap.createTransaction(parameter);
  return { token: transaction.token };
}
```

#### Proses 5: Kelola Notifikasi

```typescript
// lib/services/whatsapp.ts - WhatsApp Integration
export class WhatsAppService {
  // 5.2 Kirim WhatsApp Admin
  static async sendAdminOrderNotification(orderId, customerName, ...) {
    const message = this.generateAdminOrderNotification(...);
    await this.sendViaFonnteAPI(adminPhone, message, orderId);
  }
}
```

### 🗄️ Database Schema Mapping

#### Data Store D1: Data User

```sql
-- Prisma Schema: model User
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  phone         String?
  role          UserRole  @default(USER)
  rentals       Rental[]
  notifications Notification[]
}
```

#### Data Store D3: Data Rental

```sql
-- Prisma Schema: model Rental
model Rental {
  id               String       @id @default(cuid())
  userId           String
  productId        String
  startDate        DateTime
  endDate          DateTime
  amount           Float
  status           RentalStatus @default(PENDING)
  payment          Payment?
}
```

#### Data Store D4: Data Payment

```sql
-- Prisma Schema: model Payment
model Payment {
  id            String        @id @default(cuid())
  rentalId      String        @unique
  amount        Float
  deposit       Float
  remaining     Float
  status        PaymentStatus @default(DEPOSIT_PENDING)
  snapToken     String?
}
```

## 📋 CHECKLIST VALIDASI DFD

### ✅ Aturan Tom DeMarco

- [x] **Aturan Konservasi:** Input + Output seimbang di setiap proses
- [x] **Aturan Dekomposisi:** Child diagram konsisten dengan parent
- [x] **Aturan Interface:** Boundary jelas antara sistem dan external entity

### ✅ Aturan Gane & Sarson

- [x] **Completeness:** Semua data memiliki sumber dan tujuan yang jelas
- [x] **Consistency:** Penamaan dan penomoran konsisten di semua level
- [x] **Correctness:** Logic dapat diverifikasi dengan implementasi aktual

### ✅ Aturan Yourdon & Constantine

- [x] **Cohesion:** Setiap proses fokus pada satu fungsi spesifik
- [x] **Coupling:** Minimalisir ketergantungan antar proses
- [x] **Span of Control:** Maksimal 7±2 sub-proses per level

## 🎯 BUSINESS RULES YANG TERCERMIN

### 💰 Payment Rules

- **Deposit System:** 50% deposit untuk user baru, invoice untuk user dikenal
- **Overtime Calculation:** Biaya tambahan berdasarkan durasi operasional
- **Payment Status:** DEPOSIT_PENDING → DEPOSIT_PAID → FULLY_PAID

### 📱 Notification Rules

- **Admin Alerts:** Instant WhatsApp untuk setiap pesanan baru
- **Customer Updates:** Status update via WhatsApp dan in-app notification
- **Payment Confirmation:** Otomatis setelah payment verification

### 🔐 Security Rules

- **Role-based Access:** USER vs ADMIN dengan permission berbeda
- **Session Management:** 30 hari expiry dengan daily update
- **Payment Security:** Midtrans signature verification

## 🚀 FUTURE ENHANCEMENTS

### 📈 Scalability Considerations

- **Microservices:** Pemisahan payment dan notification service
- **Caching:** Redis untuk session dan frequent queries
- **Queue System:** Background job untuk WhatsApp notifications

### 🔄 Process Improvements

- **Real-time Updates:** WebSocket untuk live status tracking
- **AI Integration:** Predictive maintenance scheduling
- **Mobile App:** Native mobile application

---

**📝 Catatan:** DFD ini merupakan living document yang akan diupdate seiring dengan perkembangan sistem. Setiap perubahan major pada sistem harus direfleksikan dalam DFD untuk menjaga konsistensi dokumentasi.

**🔗 Related Documents:**

- [Database Schema](../prisma/schema.prisma)
- [API Documentation](./API_DOCUMENTATION.md)
- [WhatsApp Integration](./ADMIN_WHATSAPP_NOTIFICATIONS.md)
- [Better Auth Migration](./BETTER_AUTH_MIGRATION_COMPLETED.md)
